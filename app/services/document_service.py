"""
Document Service
Business logic for document management
"""

import uuid
from typing import List, Optional
from fastapi import HTT<PERSON><PERSON>x<PERSON>, UploadFile

from app.models.document import Document
from app.repositories.document_repository import DocumentRepository
from app.services.s3_service import S3Service
from app.schemas.document import (
    DocumentCreateRequest,
    DocumentResponse,
    DocumentListResponse,
    DocumentUploadRequest,
    DocumentStatusUpdateRequest
)
from app.core.logging import logger


class DocumentService:
    """Service for document-related business logic"""
    
    def __init__(self, repository: DocumentRepository, s3_service: Optional[S3Service] = None):
        """Initialize document service"""
        self.repository = repository
        self.s3_service = s3_service
    
    async def _convert_to_response(self, document: Document) -> DocumentResponse:
        """Convert document model to response schema"""
        file_url = None
        if document.file_path and self.s3_service:
            try:
                file_url = self.s3_service.get_full_s3_url(document.file_path)
            except Exception as e:
                logger.warning(f"Failed to get S3 URL for {document.file_path}: {e}")
        
        return DocumentResponse(
            id=str(document.id),
            name=document.name,
            description=document.description,
            file_type=document.file_type,
            file_size=document.file_size,
            file_path=document.file_path,
            file_url=file_url,
            user_id=str(document.user_id) if document.user_id else None,
            franchisor_id=str(document.franchisor_id) if document.franchisor_id else None,
            is_active=document.is_active,
            is_deleted=document.is_deleted,
            created_at=document.created_at,
            updated_at=document.updated_at,
            deleted_at=document.deleted_at
        )

    async def create_document(self, document_data: DocumentCreateRequest, user_id: str) -> DocumentResponse:
        """Create a new document"""
        try:
            # Prepare document data
            doc_dict = document_data.model_dump()
            doc_dict['user_id'] = user_id
            doc_dict['id'] = uuid.uuid4()
            
            # Create document
            document = await self.repository.create_document(doc_dict)
            
            # Convert to response with S3 URL
            return await self._convert_to_response(document)
            
        except Exception as e:
            logger.error(f"Error creating document: {e}")
            raise HTTPException(status_code=500, detail="Failed to create document")

    async def get_document_by_id(self, document_id: str) -> Optional[DocumentResponse]:
        """Get document by ID"""
        try:
            document = await self.repository.get_by_id(document_id)
            if not document:
                return None
            
            return await self._convert_to_response(document)
            
        except Exception as e:
            logger.error(f"Error getting document {document_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to get document")

    async def get_documents_with_pagination(
        self,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        user_id: Optional[str] = None,
        franchisor_id: Optional[str] = None,
        file_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        sort_by: Optional[str] = None,
        sort_order: Optional[str] = "desc"
    ) -> DocumentListResponse:
        """Get documents with filtering, search, and pagination"""
        try:
            documents, total_count = await self.repository.get_all_with_filters(
                skip=skip,
                limit=limit,
                search=search,
                user_id=user_id,
                franchisor_id=franchisor_id,
                file_type=file_type,
                is_active=is_active,
                sort_by=sort_by,
                sort_order=sort_order
            )
            
            # Convert to response format
            document_responses = []
            for document in documents:
                doc_response = await self._convert_to_response(document)
                document_responses.append(doc_response)
            
            # Pagination info
            pagination = {
                "page": (skip // limit) + 1,
                "limit": limit,
                "total": total_count,
                "pages": (total_count + limit - 1) // limit
            }
            
            return DocumentListResponse(
                items=document_responses,
                total_count=total_count,
                pagination=pagination
            )
            
        except Exception as e:
            logger.error(f"Error getting documents: {e}")
            raise HTTPException(status_code=500, detail="Failed to get documents")

    async def upload_file(
        self, 
        file: UploadFile,
        document_data: DocumentUploadRequest,
        user_id: str
    ) -> DocumentResponse:
        """Upload document file to S3 and create document record"""
        try:
            if not self.s3_service:
                raise HTTPException(status_code=500, detail="S3 service not available")
            
            # Upload file to S3
            file_path = await self.s3_service.upload_file(file, prefix="documents")
            
            # Get file info
            file_size = str(file.size) if file.size else "0"
            file_type = file.content_type or "application/octet-stream"
            
            # Create document record
            create_data = DocumentCreateRequest(
                name=document_data.name,
                description=document_data.description,
                file_path=file_path,
                file_type=file_type,
                file_size=file_size,
                franchisor_id=document_data.franchisor_id,
                is_active=document_data.is_active
            )
            
            # Create document in database
            document_response = await self.create_document(create_data, user_id)
            
            logger.info(f"Uploaded document: {document_response.id}")
            return document_response
            
        except Exception as e:
            logger.error(f"Error uploading document: {e}")
            raise HTTPException(status_code=500, detail="Failed to upload document")



    async def delete_document(self, document_id: str, delete_from_s3: bool = False) -> bool:
        """Soft delete document and optionally delete from S3"""
        try:
            # Get document first to get file path
            document = await self.repository.get_by_id(document_id)
            if not document:
                return False

            # Soft delete from database
            success = await self.repository.soft_delete_document(document_id)

            # Optionally delete from S3
            if success and delete_from_s3 and document.file_path and self.s3_service:
                try:
                    await self.s3_service.delete_file(document.file_path)
                    logger.info(f"Deleted file from S3: {document.file_path}")
                except Exception as e:
                    logger.warning(f"Failed to delete file from S3: {e}")

            if success:
                logger.info(f"Deleted document: {document_id}")
            return success

        except Exception as e:
            logger.error(f"Error deleting document {document_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to delete document")



    async def update_document_status(self, document_id: str, status_data: DocumentStatusUpdateRequest) -> Optional[DocumentResponse]:
        """Update document active status"""
        try:
            document = await self.repository.update_status(document_id, status_data.is_active)
            if not document:
                return None
            
            return await self._convert_to_response(document)
            
        except Exception as e:
            logger.error(f"Error updating document status {document_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to update document status")

    async def get_documents_by_franchisor(self, franchisor_id: str, is_active: Optional[bool] = None) -> List[DocumentResponse]:
        """Get documents by franchisor ID"""
        try:
            documents = await self.repository.get_by_franchisor(franchisor_id, is_active)
            
            # Convert to response format
            document_responses = []
            for document in documents:
                doc_response = await self._convert_to_response(document)
                document_responses.append(doc_response)
            
            return document_responses
            
        except Exception as e:
            logger.error(f"Error getting documents by franchisor {franchisor_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to get documents by franchisor")

    async def count_documents(
        self,
        user_id: Optional[str] = None,
        franchisor_id: Optional[str] = None,
        file_type: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> int:
        """Count documents with filters"""
        try:
            return await self.repository.count_by_filters(
                user_id=user_id,
                franchisor_id=franchisor_id,
                file_type=file_type,
                is_active=is_active
            )

        except Exception as e:
            logger.error(f"Error counting documents: {e}")
            raise HTTPException(status_code=500, detail="Failed to count documents")

    async def update_document(self, document_id: str, update_data: dict) -> Optional[DocumentResponse]:
        """Update document with provided data"""
        try:
            document = await self.repository.update_document(document_id, update_data)
            if not document:
                return None

            return await self._convert_to_response(document)

        except Exception as e:
            logger.error(f"Error updating document {document_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to update document")

    async def download_document(self, document_id: str) -> Optional[str]:
        """Get download URL for document"""
        try:
            document = await self.repository.get_by_id(document_id)
            if not document or not document.file_path:
                return None

            if self.s3_service:
                return await self.s3_service.generate_presigned_url(document.file_path)

            return None

        except Exception as e:
            logger.error(f"Error getting download URL for document {document_id}: {e}")
            raise HTTPException(status_code=500, detail="Failed to get download URL")
