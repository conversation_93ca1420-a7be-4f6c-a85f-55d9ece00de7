"""
Document API endpoints for backward compatibility
Provides endpoints at /api/documents/ path without v1 prefix
"""

import logging
from typing import Optional
from fastapi import APIRouter, Depends, Query, UploadFile, File, Form

from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.factory import get_document_service
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.services.document_service import DocumentService
from app.schemas.document import DocumentUploadRequest

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/")
async def get_documents(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of records to return"),
    search: Optional[str] = Query(None, description="Search term"),
    franchisor_id: Optional[str] = Query(None, description="Filter by franchisor ID"),
    file_type: Optional[str] = Query(None, description="Filter by file type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    sort_by: Optional[str] = Query(None, description="Sort by field: name, created_at"),
    sort_order: Optional[str] = Query("desc", description="Sort order: asc or desc"),
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Get documents with pagination and filtering (backward compatibility)"""
    try:
        documents = await document_service.get_documents_with_pagination(
            skip=skip,
            limit=limit,
            search=search,
            franchisor_id=franchisor_id,
            file_type=file_type,
            is_active=is_active,
            sort_by=sort_by,
            sort_order=sort_order
        )
        
        return create_success_response(
            data=documents.model_dump(),
            message_title="Documents Retrieved",
            message_description=f"Successfully retrieved {len(documents.items)} documents"
        )
        
    except Exception as e:
        logger.error(f"Error getting documents: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Documents",
            message_description=str(e),
            status_code=500
        )


@router.get("/{document_id}")
async def get_document_by_id(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Get document by ID (backward compatibility)"""
    try:
        document = await document_service.get_document_by_id(document_id)
        
        if not document:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found",
                status_code=404
            )
        
        return create_success_response(
            data=document.model_dump(),
            message_title="Document Retrieved",
            message_description="Document retrieved successfully"
        )
        
    except Exception as e:
        logger.error(f"Error getting document {document_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Retrieving Document",
            message_description=str(e),
            status_code=500
        )


@router.delete("/{document_id}")
async def delete_document(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Delete document (backward compatibility)"""
    try:
        success = await document_service.delete_document(document_id)
        
        if not success:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found",
                status_code=404
            )
        
        return create_success_response(
            data={"document_id": document_id, "deleted": True},
            message_title="Document Deleted",
            message_description="Document deleted successfully"
        )
        
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {e}")
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Deleting Document",
            message_description=str(e),
            status_code=500
        )


@router.post("/upload")
async def upload_document(
    file: UploadFile = File(...),
    name: str = Form(...),
    description: Optional[str] = Form(None),
    franchisor_id: Optional[str] = Form(None),
    is_active: bool = Form(True),
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Upload document (backward compatibility)"""
    try:
        # Validate file
        if not file.filename:
            return create_error_response(
                error_code=ErrorCodes.VALIDATION_ERROR,
                message_title="No File Provided",
                message_description="Please provide a file to upload",
                status_code=400
            )
        
        # Create upload request
        upload_request = DocumentUploadRequest(
            name=name,
            description=description,
            franchisor_id=franchisor_id,
            is_active=is_active
        )
        
        # Upload file
        document = await document_service.upload_file(
            file=file,
            upload_request=upload_request,
            user_id=current_user["user_id"]
        )
        
        return create_success_response(
            data=document.model_dump(),
            message_title="Document Uploaded",
            message_description="Document uploaded successfully",
            status_code=201
        )
        
    except Exception as e:
        logger.error(f"Error uploading document: {e}")
        return create_error_response(
            error_code=ErrorCodes.UPLOAD_ERROR,
            message_title="Upload Failed",
            message_description=str(e),
            status_code=500
        )


@router.get("/{document_id}/status")
async def get_document_status(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Get document status (backward compatibility)"""
    try:
        document = await document_service.get_document_by_id(document_id)
        
        if not document:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found",
                status_code=404
            )
        
        return create_success_response(
            data={
                "document_id": document.id,
                "name": document.name,
                "is_active": document.is_active,
                "is_deleted": document.is_deleted,
                "created_at": document.created_at,
                "updated_at": document.updated_at,
                "deleted_at": document.deleted_at
            },
            message_title="Document Status Retrieved",
            message_description="Document status retrieved successfully"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Getting Document Status",
            message_description=str(e),
            status_code=500
        )


@router.get("/{document_id}/download")
async def download_document(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Get download URL for document (backward compatibility)"""
    try:
        download_url = await document_service.download_document(document_id)
        
        if not download_url:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found or has no file",
                status_code=404
            )
        
        return create_success_response(
            data={"download_url": download_url},
            message_title="Download URL Generated",
            message_description="Document download URL generated successfully"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Getting Download URL",
            message_description=str(e),
            status_code=500
        )
