"""
Document API endpoints for backward compatibility
Provides endpoints at /api/documents/ path without v1 prefix
"""

import logging
from fastapi import APIRouter, Depends

from app.core.security.enhanced_auth_middleware import get_current_user
from app.core.factory import get_document_service
from app.core.responses import create_success_response, create_error_response, ErrorCodes
from app.services.document_service import DocumentService

router = APIRouter()
logger = logging.getLogger(__name__)


@router.get("/{document_id}/status")
async def get_document_status(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Get document status"""
    try:
        document = await document_service.get_document_by_id(document_id)
        
        if not document:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found",
                status_code=404
            )
        
        return create_success_response(
            data={
                "document_id": document.id,
                "name": document.name,
                "is_active": document.is_active,
                "is_deleted": document.is_deleted,
                "created_at": document.created_at,
                "updated_at": document.updated_at,
                "deleted_at": document.deleted_at
            },
            message_title="Document Status Retrieved",
            message_description="Document status retrieved successfully"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Getting Document Status",
            message_description=str(e),
            status_code=500
        )


@router.get("/{document_id}/download")
async def download_document(
    document_id: str,
    current_user: dict = Depends(get_current_user),
    document_service: DocumentService = Depends(get_document_service)
):
    """Get download URL for document"""
    try:
        download_url = await document_service.download_document(document_id)
        
        if not download_url:
            return create_error_response(
                error_code=ErrorCodes.NOT_FOUND,
                message_title="Document Not Found",
                message_description=f"Document with ID {document_id} not found or has no file",
                status_code=404
            )
        
        return create_success_response(
            data={"download_url": download_url},
            message_title="Download URL Generated",
            message_description="Document download URL generated successfully"
        )
        
    except Exception as e:
        return create_error_response(
            error_code=ErrorCodes.DATABASE_ERROR,
            message_title="Error Getting Download URL",
            message_description=str(e),
            status_code=500
        )
