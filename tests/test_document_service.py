"""
Test Document Service
Comprehensive tests for document service operations
"""

import pytest
import uuid
from unittest.mock import AsyncMock, Mock
from fastapi import HTT<PERSON><PERSON>x<PERSON>, UploadFile

from app.services.document_service import DocumentService
from app.repositories.document_repository import DocumentRepository
from app.services.s3_service import S3Service
from app.models.document import Document
from app.schemas.document import (
    DocumentCreateRequest,
    DocumentResponse,
    DocumentUploadRequest,
    DocumentStatusUpdateRequest
)


@pytest.fixture
def mock_repository():
    """Mock document repository"""
    return AsyncMock(spec=DocumentRepository)


@pytest.fixture
def mock_s3_service():
    """Mock S3 service"""
    return AsyncMock(spec=S3Service)


@pytest.fixture
def document_service(mock_repository, mock_s3_service):
    """Create document service with mocked dependencies"""
    return DocumentService(mock_repository, mock_s3_service)


@pytest.fixture
def sample_document():
    """Sample document for testing"""
    return Document(
        id=uuid.uuid4(),
        name="Test Document",
        description="Test description",
        file_path="documents/test.pdf",
        file_type="application/pdf",
        file_size="1024",
        user_id=uuid.uuid4(),
        franchisor_id=uuid.uuid4(),
        is_active=True,
        is_deleted=False
    )


@pytest.fixture
def sample_create_request():
    """Sample document create request"""
    return DocumentCreateRequest(
        name="Test Document",
        description="Test description",
        file_path="documents/test.pdf",
        file_type="application/pdf",
        file_size="1024",
        franchisor_id=str(uuid.uuid4()),
        is_active=True
    )


@pytest.fixture
def mock_upload_file():
    """Mock upload file"""
    mock_file = Mock(spec=UploadFile)
    mock_file.filename = "test.pdf"
    mock_file.size = 1024
    mock_file.content_type = "application/pdf"
    return mock_file


class TestDocumentService:
    """Test document service operations"""

    @pytest.mark.asyncio
    async def test_create_document(self, document_service, mock_repository, sample_create_request, sample_document):
        """Test creating a new document"""
        # Setup mocks
        mock_repository.create_document.return_value = sample_document
        document_service.s3_service.get_full_s3_url.return_value = "https://s3.amazonaws.com/test.pdf"
        
        # Test document creation
        result = await document_service.create_document(sample_create_request, str(uuid.uuid4()))
        
        # Assertions
        assert isinstance(result, DocumentResponse)
        assert result.name == sample_document.name
        mock_repository.create_document.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_document_by_id_found(self, document_service, mock_repository, sample_document):
        """Test getting document by ID when found"""
        # Setup mocks
        mock_repository.get_by_id.return_value = sample_document
        document_service.s3_service.get_full_s3_url.return_value = "https://s3.amazonaws.com/test.pdf"
        
        # Test getting document
        result = await document_service.get_document_by_id(str(sample_document.id))
        
        # Assertions
        assert isinstance(result, DocumentResponse)
        assert result.id == str(sample_document.id)
        mock_repository.get_by_id.assert_called_once_with(str(sample_document.id))

    @pytest.mark.asyncio
    async def test_get_document_by_id_not_found(self, document_service, mock_repository):
        """Test getting document by ID when not found"""
        # Setup mocks
        mock_repository.get_by_id.return_value = None
        
        # Test getting document
        result = await document_service.get_document_by_id(str(uuid.uuid4()))
        
        # Assertions
        assert result is None
        mock_repository.get_by_id.assert_called_once()

    @pytest.mark.asyncio
    async def test_upload_file(self, document_service, mock_repository, mock_s3_service, mock_upload_file, sample_document):
        """Test uploading a file"""
        # Setup mocks
        mock_s3_service.upload_file.return_value = "documents/test.pdf"
        mock_repository.create_document.return_value = sample_document
        document_service.s3_service.get_full_s3_url.return_value = "https://s3.amazonaws.com/test.pdf"
        
        upload_request = DocumentUploadRequest(
            name="Test Document",
            description="Test description",
            franchisor_id=str(uuid.uuid4()),
            is_active=True
        )
        
        # Test file upload
        result = await document_service.upload_file(mock_upload_file, upload_request, str(uuid.uuid4()))
        
        # Assertions
        assert isinstance(result, DocumentResponse)
        mock_s3_service.upload_file.assert_called_once()
        mock_repository.create_document.assert_called_once()

    @pytest.mark.asyncio
    async def test_upload_file_no_s3_service(self, mock_repository):
        """Test uploading file without S3 service"""
        # Create service without S3
        service = DocumentService(mock_repository, None)
        
        upload_request = DocumentUploadRequest(
            name="Test Document",
            description="Test description",
            franchisor_id=str(uuid.uuid4()),
            is_active=True
        )
        
        # Test file upload should raise exception
        with pytest.raises(HTTPException) as exc_info:
            await service.upload_file(Mock(), upload_request, str(uuid.uuid4()))
        
        assert exc_info.value.status_code == 500
        assert "S3 service not available" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_delete_document(self, document_service, mock_repository, sample_document):
        """Test deleting a document"""
        # Setup mocks
        mock_repository.get_by_id.return_value = sample_document
        mock_repository.soft_delete_document.return_value = True
        
        # Test document deletion
        result = await document_service.delete_document(str(sample_document.id))
        
        # Assertions
        assert result is True
        mock_repository.get_by_id.assert_called_once()
        mock_repository.soft_delete_document.assert_called_once()

    @pytest.mark.asyncio
    async def test_delete_document_with_s3_deletion(self, document_service, mock_repository, mock_s3_service, sample_document):
        """Test deleting document with S3 file deletion"""
        # Setup mocks
        mock_repository.get_by_id.return_value = sample_document
        mock_repository.soft_delete_document.return_value = True
        mock_s3_service.delete_file.return_value = True
        
        # Test document deletion with S3 deletion
        result = await document_service.delete_document(str(sample_document.id), delete_from_s3=True)
        
        # Assertions
        assert result is True
        mock_repository.get_by_id.assert_called_once()
        mock_repository.soft_delete_document.assert_called_once()
        mock_s3_service.delete_file.assert_called_once_with(sample_document.file_path)

    @pytest.mark.asyncio
    async def test_update_document_status(self, document_service, mock_repository, sample_document):
        """Test updating document status"""
        # Setup mocks
        mock_repository.update_status.return_value = sample_document
        document_service.s3_service.get_full_s3_url.return_value = "https://s3.amazonaws.com/test.pdf"
        
        status_request = DocumentStatusUpdateRequest(is_active=False)
        
        # Test status update
        result = await document_service.update_document_status(str(sample_document.id), status_request)
        
        # Assertions
        assert isinstance(result, DocumentResponse)
        mock_repository.update_status.assert_called_once_with(str(sample_document.id), False)

    @pytest.mark.asyncio
    async def test_download_document(self, document_service, mock_repository, mock_s3_service, sample_document):
        """Test getting download URL for document"""
        # Setup mocks
        mock_repository.get_by_id.return_value = sample_document
        mock_s3_service.generate_presigned_url.return_value = "https://presigned-url.com"
        
        # Test download URL generation
        result = await document_service.download_document(str(sample_document.id))
        
        # Assertions
        assert result == "https://presigned-url.com"
        mock_repository.get_by_id.assert_called_once()
        mock_s3_service.generate_presigned_url.assert_called_once_with(sample_document.file_path)

    @pytest.mark.asyncio
    async def test_download_document_not_found(self, document_service, mock_repository):
        """Test download URL for non-existent document"""
        # Setup mocks
        mock_repository.get_by_id.return_value = None
        
        # Test download URL generation
        result = await document_service.download_document(str(uuid.uuid4()))
        
        # Assertions
        assert result is None
        mock_repository.get_by_id.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_documents_by_franchisor(self, document_service, mock_repository, sample_document):
        """Test getting documents by franchisor"""
        # Setup mocks
        mock_repository.get_by_franchisor.return_value = [sample_document]
        document_service.s3_service.get_full_s3_url.return_value = "https://s3.amazonaws.com/test.pdf"
        
        # Test getting documents by franchisor
        result = await document_service.get_documents_by_franchisor(str(sample_document.franchisor_id))
        
        # Assertions
        assert len(result) == 1
        assert isinstance(result[0], DocumentResponse)
        mock_repository.get_by_franchisor.assert_called_once()

    @pytest.mark.asyncio
    async def test_count_documents(self, document_service, mock_repository):
        """Test counting documents"""
        # Setup mocks
        mock_repository.count_by_filters.return_value = 5
        
        # Test counting documents
        result = await document_service.count_documents(is_active=True)
        
        # Assertions
        assert result == 5
        mock_repository.count_by_filters.assert_called_once_with(
            user_id=None,
            franchisor_id=None,
            file_type=None,
            is_active=True
        )
