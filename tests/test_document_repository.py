"""
Test Document Repository
Comprehensive tests for document repository operations
"""

import pytest
import uuid
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from unittest.mock import AsyncMock

from app.models.document import Document
from app.repositories.document_repository import DocumentRepository
from app.schemas.document import DocumentCreateRequest


@pytest.fixture
def document_repository(mock_db_session):
    """Create document repository with mocked database session"""
    return DocumentRepository(mock_db_session)


@pytest.fixture
def sample_document_data():
    """Sample document data for testing"""
    return {
        "id": uuid.uuid4(),
        "name": "Test Document",
        "description": "Test document description",
        "file_path": "documents/test.pdf",
        "file_type": "application/pdf",
        "file_size": "1024",
        "user_id": uuid.uuid4(),
        "franchisor_id": uuid.uuid4(),
        "is_active": True,
        "is_deleted": False,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    }


@pytest.fixture
def sample_document(sample_document_data):
    """Create sample document instance"""
    return Document(**sample_document_data)


class TestDocumentRepository:
    """Test document repository operations"""

    @pytest.mark.asyncio
    async def test_create_document(self, document_repository, sample_document_data):
        """Test creating a new document"""
        # Mock database operations
        document_repository.db.add = AsyncMock()
        document_repository.db.commit = AsyncMock()
        document_repository.db.refresh = AsyncMock()
        
        # Test document creation
        result = await document_repository.create_document(sample_document_data)
        
        # Assertions
        assert result is not None
        assert isinstance(result, Document)
        document_repository.db.add.assert_called_once()
        document_repository.db.commit.assert_called_once()
        document_repository.db.refresh.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_by_id_found(self, document_repository, sample_document):
        """Test getting document by ID when found"""
        # Mock database query
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = sample_document
        document_repository.db.execute = AsyncMock(return_value=mock_result)
        
        # Test getting document
        result = await document_repository.get_by_id(str(sample_document.id))
        
        # Assertions
        assert result == sample_document
        document_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_by_id_not_found(self, document_repository):
        """Test getting document by ID when not found"""
        # Mock database query
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        document_repository.db.execute = AsyncMock(return_value=mock_result)
        
        # Test getting document
        result = await document_repository.get_by_id(str(uuid.uuid4()))
        
        # Assertions
        assert result is None
        document_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_all_with_filters(self, document_repository, sample_document):
        """Test getting documents with filters"""
        # Mock database queries
        mock_result = AsyncMock()
        mock_result.scalars.return_value.all.return_value = [sample_document]
        mock_count_result = AsyncMock()
        mock_count_result.scalar.return_value = 1
        
        document_repository.db.execute = AsyncMock(side_effect=[mock_count_result, mock_result])
        
        # Test getting documents with filters
        documents, total_count = await document_repository.get_all_with_filters(
            skip=0,
            limit=10,
            search="test",
            is_active=True
        )
        
        # Assertions
        assert len(documents) == 1
        assert documents[0] == sample_document
        assert total_count == 1
        assert document_repository.db.execute.call_count == 2

    @pytest.mark.asyncio
    async def test_soft_delete_document(self, document_repository):
        """Test soft deleting a document"""
        # Mock database operations
        mock_result = AsyncMock()
        mock_result.rowcount = 1
        document_repository.db.execute = AsyncMock(return_value=mock_result)
        document_repository.db.commit = AsyncMock()
        
        # Test soft delete
        result = await document_repository.soft_delete_document(str(uuid.uuid4()))
        
        # Assertions
        assert result is True
        document_repository.db.execute.assert_called_once()
        document_repository.db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_document(self, document_repository, sample_document):
        """Test updating a document"""
        # Mock database operations
        mock_result = AsyncMock()
        mock_result.rowcount = 1
        document_repository.db.execute = AsyncMock(return_value=mock_result)
        document_repository.db.commit = AsyncMock()
        document_repository.get_by_id = AsyncMock(return_value=sample_document)
        
        # Test update
        update_data = {"name": "Updated Document"}
        result = await document_repository.update_document(str(sample_document.id), update_data)
        
        # Assertions
        assert result == sample_document
        document_repository.db.execute.assert_called_once()
        document_repository.db.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_by_franchisor(self, document_repository, sample_document):
        """Test getting documents by franchisor ID"""
        # Mock database query
        mock_result = AsyncMock()
        mock_result.scalars.return_value.all.return_value = [sample_document]
        document_repository.db.execute = AsyncMock(return_value=mock_result)
        
        # Test getting documents by franchisor
        result = await document_repository.get_by_franchisor(str(sample_document.franchisor_id))
        
        # Assertions
        assert len(result) == 1
        assert result[0] == sample_document
        document_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_count_by_filters(self, document_repository):
        """Test counting documents with filters"""
        # Mock database query
        mock_result = AsyncMock()
        mock_result.scalar.return_value = 5
        document_repository.db.execute = AsyncMock(return_value=mock_result)
        
        # Test counting documents
        result = await document_repository.count_by_filters(is_active=True)
        
        # Assertions
        assert result == 5
        document_repository.db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_status(self, document_repository, sample_document):
        """Test updating document status"""
        # Mock database operations
        mock_result = AsyncMock()
        mock_result.rowcount = 1
        document_repository.db.execute = AsyncMock(return_value=mock_result)
        document_repository.db.commit = AsyncMock()
        document_repository.get_by_id = AsyncMock(return_value=sample_document)
        
        # Test status update
        result = await document_repository.update_status(str(sample_document.id), False)
        
        # Assertions
        assert result == sample_document
        document_repository.db.execute.assert_called_once()
        document_repository.db.commit.assert_called_once()
