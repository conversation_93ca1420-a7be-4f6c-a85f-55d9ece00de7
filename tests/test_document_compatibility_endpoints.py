"""
Test Document Compatibility Endpoints
Tests for backward compatibility endpoints at /api/documents/
"""

import pytest
import uuid
from unittest.mock import AsyncMock, Mock
from fastapi.testclient import TestClient

from app.main import app
from app.services.document_service import DocumentService
from app.schemas.document import DocumentResponse, DocumentListResponse


@pytest.fixture
def client():
    """Test client"""
    return TestClient(app)


@pytest.fixture
def mock_document_service():
    """Mock document service"""
    return AsyncMock(spec=DocumentService)


@pytest.fixture
def mock_current_user():
    """Mock current user"""
    return {"user_id": str(uuid.uuid4()), "email": "<EMAIL>"}


@pytest.fixture
def sample_document_response():
    """Sample document response"""
    return DocumentResponse(
        id=str(uuid.uuid4()),
        name="Test Document",
        description="Test description",
        file_type="application/pdf",
        file_size="1024",
        file_path="documents/test.pdf",
        file_url="https://s3.amazonaws.com/test.pdf",
        user_id=str(uuid.uuid4()),
        franchisor_id=str(uuid.uuid4()),
        is_active=True,
        is_deleted=False
    )


class TestDocumentCompatibilityEndpoints:
    """Test backward compatibility document endpoints"""

    def test_get_documents_compat(self, client, mock_document_service, mock_current_user, sample_document_response, monkeypatch):
        """Test GET /api/documents/"""
        # Mock dependencies
        monkeypatch.setattr("app.api.documents_compat.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.documents_compat.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_list_response = DocumentListResponse(
            items=[sample_document_response],
            total_count=1,
            pagination={"page": 1, "limit": 20, "total": 1, "pages": 1}
        )
        mock_document_service.get_documents_with_pagination.return_value = mock_list_response
        
        # Make request
        response = client.get("/api/documents/?skip=0&limit=20")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert len(data["data"]["items"]) == 1

    def test_get_document_by_id_compat(self, client, mock_document_service, mock_current_user, sample_document_response, monkeypatch):
        """Test GET /api/documents/{document_id}"""
        # Mock dependencies
        monkeypatch.setattr("app.api.documents_compat.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.documents_compat.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.get_document_by_id.return_value = sample_document_response
        
        # Make request
        response = client.get(f"/api/documents/{sample_document_response.id}")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == sample_document_response.id

    def test_delete_document_compat(self, client, mock_document_service, mock_current_user, monkeypatch):
        """Test DELETE /api/documents/{document_id}"""
        # Mock dependencies
        monkeypatch.setattr("app.api.documents_compat.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.documents_compat.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.delete_document.return_value = True
        
        document_id = str(uuid.uuid4())
        
        # Make request
        response = client.delete(f"/api/documents/{document_id}")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["deleted"] is True

    def test_upload_document_compat(self, client, mock_document_service, mock_current_user, sample_document_response, monkeypatch):
        """Test POST /api/documents/upload"""
        # Mock dependencies
        monkeypatch.setattr("app.api.documents_compat.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.documents_compat.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.upload_file.return_value = sample_document_response
        
        # Prepare test file
        test_file_content = b"test file content"
        files = {"file": ("test.pdf", test_file_content, "application/pdf")}
        data = {
            "name": "Test Document",
            "description": "Test description",
            "is_active": "true"
        }
        
        # Make request
        response = client.post("/api/documents/upload", files=files, data=data)
        
        # Assertions
        assert response.status_code == 201
        response_data = response.json()
        assert response_data["success"] is True
        assert response_data["data"]["name"] == "Test Document"

    def test_get_document_status_compat(self, client, mock_document_service, mock_current_user, sample_document_response, monkeypatch):
        """Test GET /api/documents/{document_id}/status"""
        # Mock dependencies
        monkeypatch.setattr("app.api.documents_compat.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.documents_compat.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.get_document_by_id.return_value = sample_document_response
        
        # Make request
        response = client.get(f"/api/documents/{sample_document_response.id}/status")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["document_id"] == sample_document_response.id
        assert "is_active" in data["data"]
        assert "is_deleted" in data["data"]

    def test_download_document_compat(self, client, mock_document_service, mock_current_user, monkeypatch):
        """Test GET /api/documents/{document_id}/download"""
        # Mock dependencies
        monkeypatch.setattr("app.api.documents_compat.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.documents_compat.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.download_document.return_value = "https://presigned-url.com"
        
        document_id = str(uuid.uuid4())
        
        # Make request
        response = client.get(f"/api/documents/{document_id}/download")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["download_url"] == "https://presigned-url.com"

    def test_endpoints_require_authentication(self, client):
        """Test that all endpoints require authentication"""
        document_id = str(uuid.uuid4())
        
        # Test endpoints without authentication
        endpoints = [
            ("GET", "/api/documents/"),
            ("GET", f"/api/documents/{document_id}"),
            ("DELETE", f"/api/documents/{document_id}"),
            ("GET", f"/api/documents/{document_id}/status"),
            ("GET", f"/api/documents/{document_id}/download"),
        ]
        
        for method, endpoint in endpoints:
            if method == "GET":
                response = client.get(endpoint)
            elif method == "DELETE":
                response = client.delete(endpoint)
            
            # Should return 401 Unauthorized
            assert response.status_code == 401, f"Endpoint {method} {endpoint} should require authentication"

    def test_upload_without_file(self, client, mock_current_user, monkeypatch):
        """Test upload endpoint without file"""
        # Mock dependencies
        monkeypatch.setattr("app.api.documents_compat.get_current_user", lambda: mock_current_user)
        
        # Make request without file
        data = {"name": "Test Document"}
        response = client.post("/api/documents/upload", data=data)
        
        # Should return validation error
        assert response.status_code == 422
