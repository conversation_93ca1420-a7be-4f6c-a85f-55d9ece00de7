"""
Test Document Endpoints
Comprehensive tests for document API endpoints
"""

import pytest
import uuid
from unittest.mock import AsyncMock, Mock
from fastapi.testclient import TestClient
from fastapi import UploadFile

from app.main import app
from app.services.document_service import DocumentService
from app.schemas.document import DocumentResponse, DocumentListResponse


@pytest.fixture
def client():
    """Test client"""
    return TestClient(app)


@pytest.fixture
def mock_document_service():
    """Mock document service"""
    return AsyncMock(spec=DocumentService)


@pytest.fixture
def mock_current_user():
    """Mock current user"""
    return {"user_id": str(uuid.uuid4()), "email": "<EMAIL>"}


@pytest.fixture
def sample_document_response():
    """Sample document response"""
    return DocumentResponse(
        id=str(uuid.uuid4()),
        name="Test Document",
        description="Test description",
        file_type="application/pdf",
        file_size="1024",
        file_path="documents/test.pdf",
        file_url="https://s3.amazonaws.com/test.pdf",
        user_id=str(uuid.uuid4()),
        franchisor_id=str(uuid.uuid4()),
        is_active=True,
        is_deleted=False
    )


class TestDocumentEndpoints:
    """Test document API endpoints"""

    def test_get_documents_success(self, client, mock_document_service, mock_current_user, sample_document_response, monkeypatch):
        """Test getting documents with pagination"""
        # Mock dependencies
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_list_response = DocumentListResponse(
            items=[sample_document_response],
            total_count=1,
            pagination={"page": 1, "limit": 20, "total": 1, "pages": 1}
        )
        mock_document_service.get_documents_with_pagination.return_value = mock_list_response
        
        # Make request
        response = client.get("/api/v1/documents?skip=0&limit=20")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert len(data["data"]["items"]) == 1

    def test_get_document_by_id_success(self, client, mock_document_service, mock_current_user, sample_document_response, monkeypatch):
        """Test getting document by ID"""
        # Mock dependencies
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.get_document_by_id.return_value = sample_document_response
        
        # Make request
        response = client.get(f"/api/v1/documents/{sample_document_response.id}")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == sample_document_response.id

    def test_get_document_by_id_not_found(self, client, mock_document_service, mock_current_user, monkeypatch):
        """Test getting non-existent document"""
        # Mock dependencies
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.get_document_by_id.return_value = None
        
        # Make request
        response = client.get(f"/api/v1/documents/{uuid.uuid4()}")
        
        # Assertions
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False
        assert "not found" in data["message"]["description"].lower()

    def test_upload_document_success(self, client, mock_document_service, mock_current_user, sample_document_response, monkeypatch):
        """Test uploading a document"""
        # Mock dependencies
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.upload_file.return_value = sample_document_response
        
        # Prepare test file
        test_file_content = b"test file content"
        files = {"file": ("test.pdf", test_file_content, "application/pdf")}
        data = {
            "name": "Test Document",
            "description": "Test description",
            "is_active": "true"
        }
        
        # Make request
        response = client.post("/api/v1/documents/upload", files=files, data=data)
        
        # Assertions
        assert response.status_code == 201
        response_data = response.json()
        assert response_data["success"] is True
        assert response_data["data"]["name"] == "Test Document"

    def test_upload_document_no_file(self, client, mock_current_user, monkeypatch):
        """Test uploading without file"""
        # Mock dependencies
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_current_user", lambda: mock_current_user)
        
        # Make request without file
        data = {"name": "Test Document"}
        response = client.post("/api/v1/documents/upload", data=data)
        
        # Assertions
        assert response.status_code == 422  # Validation error

    def test_delete_document_success(self, client, mock_document_service, mock_current_user, monkeypatch):
        """Test deleting a document"""
        # Mock dependencies
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.delete_document.return_value = True
        
        document_id = str(uuid.uuid4())
        
        # Make request
        response = client.delete(f"/api/v1/documents/{document_id}")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["deleted"] is True

    def test_delete_document_not_found(self, client, mock_document_service, mock_current_user, monkeypatch):
        """Test deleting non-existent document"""
        # Mock dependencies
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.delete_document.return_value = False
        
        document_id = str(uuid.uuid4())
        
        # Make request
        response = client.delete(f"/api/v1/documents/{document_id}")
        
        # Assertions
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False

    def test_update_document_status_success(self, client, mock_document_service, mock_current_user, sample_document_response, monkeypatch):
        """Test updating document status"""
        # Mock dependencies
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        updated_response = sample_document_response.model_copy()
        updated_response.is_active = False
        mock_document_service.update_document_status.return_value = updated_response
        
        # Make request
        response = client.patch(
            f"/api/v1/documents/{sample_document_response.id}/status",
            json={"is_active": False}
        )
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["is_active"] is False

    def test_download_document_success(self, client, mock_document_service, mock_current_user, monkeypatch):
        """Test getting download URL for document"""
        # Mock dependencies
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.download_document.return_value = "https://presigned-url.com"
        
        document_id = str(uuid.uuid4())
        
        # Make request
        response = client.get(f"/api/v1/documents/{document_id}/download")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["download_url"] == "https://presigned-url.com"

    def test_download_document_not_found(self, client, mock_document_service, mock_current_user, monkeypatch):
        """Test download URL for non-existent document"""
        # Mock dependencies
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        mock_document_service.download_document.return_value = None
        
        document_id = str(uuid.uuid4())
        
        # Make request
        response = client.get(f"/api/v1/documents/{document_id}/download")
        
        # Assertions
        assert response.status_code == 404
        data = response.json()
        assert data["success"] is False

    def test_update_document_success(self, client, mock_document_service, mock_current_user, sample_document_response, monkeypatch):
        """Test updating document information"""
        # Mock dependencies
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_current_user", lambda: mock_current_user)
        monkeypatch.setattr("app.api.v1.endpoints.documents.get_document_service", lambda: mock_document_service)
        
        # Setup mock response
        updated_response = sample_document_response.model_copy()
        updated_response.name = "Updated Document"
        mock_document_service.update_document.return_value = updated_response
        
        # Make request
        data = {"name": "Updated Document", "description": "Updated description"}
        response = client.put(f"/api/v1/documents/{sample_document_response.id}", data=data)
        
        # Assertions
        assert response.status_code == 200
        response_data = response.json()
        assert response_data["success"] is True
        assert response_data["data"]["name"] == "Updated Document"
