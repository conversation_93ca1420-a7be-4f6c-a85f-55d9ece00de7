"""
Test configuration and fixtures
"""
import os
import sys
import pytest
import pytest_asyncio
from typing import AsyncGenerator
from httpx import AsyncClient, ASGITransport
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import Null<PERSON>ool
from datetime import datetime, timezone
from fastapi.testclient import TestClient

# Add the project root directory to Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app.core.database.connection import Base
from app.models.user import User
from app.core.security import password_hasher
from app.main import app
from app.core.database.connection import get_db
from app.models import base as models_base

# Create async test engine with proper test database configuration
TEST_DATABASE_URL = os.getenv(
    "TEST_DATABASE_URL",
    "postgresql+asyncpg://postgres:password@localhost:5432/growthhive_test"
)

engine = create_async_engine(
    TEST_DATABASE_URL,
    echo=False,  # Disable echo for tests to reduce noise
    poolclass=NullPool,  # Use NullPool for tests to avoid connection issues
    future=True
)

# Create test session factory
TestingSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False
)

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    import asyncio

    # Create a new event loop for the test session
    policy = asyncio.get_event_loop_policy()
    loop = policy.new_event_loop()

    # Set the event loop for the current thread
    asyncio.set_event_loop(loop)

    try:
        yield loop
    finally:
        # Clean up
        try:
            # Cancel all running tasks
            pending = asyncio.all_tasks(loop)
            for task in pending:
                task.cancel()

            # Wait for all tasks to complete
            if pending:
                loop.run_until_complete(asyncio.gather(*pending, return_exceptions=True))
        except Exception:
            pass
        finally:
            loop.close()

@pytest_asyncio.fixture(scope="session", autouse=True)
async def setup_test_db():
    async with engine.begin() as conn:
        await conn.run_sync(models_base.Base.metadata.create_all)
    yield
    async with engine.begin() as conn:
        await conn.run_sync(models_base.Base.metadata.drop_all)

@pytest_asyncio.fixture
async def test_session():
    """Create a test database session"""
    async with TestingSessionLocal() as session:
        yield session

@pytest.fixture
def test_client():
    """Create a test client"""
    return TestClient(app)

@pytest_asyncio.fixture
async def override_get_db(test_session):
    """Override get_db dependency"""
    async def _override_get_db():
        yield test_session
    app.dependency_overrides[get_db] = _override_get_db
    yield
    app.dependency_overrides.clear()

@pytest_asyncio.fixture(scope="session")
async def test_db_async() -> AsyncGenerator:
    """Create test database tables."""
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)
    yield
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

@pytest_asyncio.fixture
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """Create a fresh database session for each test."""
    async with TestingSessionLocal() as session:
        try:
            yield session
        finally:
            await session.rollback()
            await session.close()
        await session.rollback()

@pytest_asyncio.fixture
async def test_user(db_session: AsyncSession) -> User:
    """Create a test user."""
    user = User(
        email="<EMAIL>",
        password_hash=password_hasher.hash_password("testpassword"),
        is_active=True,
        is_email_verified=True,
        role="ADMIN",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )
    db_session.add(user)
    await db_session.commit()
    await db_session.refresh(user)
    return user

@pytest_asyncio.fixture
async def client_async() -> AsyncGenerator[AsyncClient, None]:
    """Create an async client for testing."""
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        yield client

@pytest_asyncio.fixture
async def auth_headers(client_async: AsyncClient, test_user: User) -> dict:
    """Get authentication headers for test user."""
    response = await client_async.post(
        "/auth/login",
        json={
            "email": test_user.email,
            "password": "testpassword",
            "remember_me": False
        }
    )
    token = response.json()["data"]["access_token"]
    return {"Authorization": f"Bearer {token}"}

@pytest_asyncio.fixture
async def authenticated_client():
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as client:
        # Try to register the user via the API (ignore if already exists)
        register_payload = {
            "email": "<EMAIL>",
            "password": "Admin@1234",
            "confirm_password": "Admin@1234",
            "mobile": "9999999999",
            "first_name": "Test",
            "last_name": "User"
        }
        await client.post("/api/auth/register", json=register_payload)
        # Now login
        login_payload = {
            "email_or_mobile": "<EMAIL>",
            "password": "Admin@1234",
            "remember_me": True
        }
        login_resp = await client.post("/api/auth/login", json=login_payload)
        login_json = login_resp.json()
        print("LOGIN RESPONSE:", login_json)
        if not login_resp.status_code == 200 or not login_json.get("data") or not login_json["data"].get("details"):
            raise RuntimeError(f"Login failed in test fixture: {login_json}")
        token = login_json["data"]["details"]["access_token"]
        client.headers["Authorization"] = f"Bearer {token}"
        yield client

@pytest.fixture
async def category_id(authenticated_client):
    payload = {"name": "Fixture Category", "description": "For subcategory tests."}
    resp = await authenticated_client.post("/api/categories", json=payload)
    return resp.json()["data"]["id"]


@pytest.fixture
def mock_db_session():
    """Mock database session for testing"""
    from unittest.mock import AsyncMock
    session = AsyncMock(spec=AsyncSession)
    session.add = AsyncMock()
    session.commit = AsyncMock()
    session.rollback = AsyncMock()
    session.refresh = AsyncMock()
    session.execute = AsyncMock()
    return session