# Document Management API Documentation

## Overview

The Document Management API provides comprehensive CRUD operations for managing documents in the GrowthHive system. This is a clean document management system focused on file storage, retrieval, and basic operations without ingestion processing.

## Base URL
```
/api/v1/documents
```

## Authentication
All endpoints require Bearer token authentication.

## Endpoints

### 1. Get Documents with Pagination
**GET** `/api/v1/documents`

Retrieve documents with filtering, search, and pagination capabilities.

**Query Parameters:**
- `skip` (int, optional): Number of records to skip (default: 0)
- `limit` (int, optional): Number of records to return (default: 20, max: 100)
- `search` (string, optional): Search term for name and description
- `franchisor_id` (string, optional): Filter by franchisor ID
- `file_type` (string, optional): Filter by file type
- `is_active` (boolean, optional): Filter by active status
- `sort_by` (string, optional): Sort by field (name, created_at)
- `sort_order` (string, optional): Sort order (asc, desc)

**Response:**
```json
{
  "success": true,
  "data": {
    "items": [
      {
        "id": "uuid",
        "name": "Document Name",
        "description": "Document description",
        "file_type": "application/pdf",
        "file_size": "1024",
        "file_path": "documents/file.pdf",
        "file_url": "https://s3.amazonaws.com/bucket/file.pdf",
        "user_id": "uuid",
        "franchisor_id": "uuid",
        "is_active": true,
        "is_deleted": false,
        "created_at": "2025-01-01T00:00:00Z",
        "updated_at": "2025-01-01T00:00:00Z",
        "deleted_at": null
      }
    ],
    "total_count": 100,
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### 2. Get Document by ID
**GET** `/api/v1/documents/{document_id}`

Retrieve a specific document by its ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Document Name",
    "description": "Document description",
    "file_type": "application/pdf",
    "file_size": "1024",
    "file_path": "documents/file.pdf",
    "file_url": "https://s3.amazonaws.com/bucket/file.pdf",
    "user_id": "uuid",
    "franchisor_id": "uuid",
    "is_active": true,
    "is_deleted": false,
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-01T00:00:00Z",
    "deleted_at": null
  }
}
```

### 3. Upload Document
**POST** `/api/v1/documents/upload`

Upload a new document file.

**Request (multipart/form-data):**
- `file` (file): The document file to upload
- `name` (string): Document name
- `description` (string, optional): Document description
- `franchisor_id` (string, optional): Associated franchisor ID
- `is_active` (boolean, optional): Whether document is active (default: true)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Document Name",
    "description": "Document description",
    "file_type": "application/pdf",
    "file_size": "1024",
    "file_path": "documents/file.pdf",
    "file_url": "https://s3.amazonaws.com/bucket/file.pdf",
    "user_id": "uuid",
    "franchisor_id": "uuid",
    "is_active": true,
    "is_deleted": false,
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-01T00:00:00Z",
    "deleted_at": null
  }
}
```

### 4. Update Document
**PUT** `/api/v1/documents/{document_id}`

Update document information.

**Request (multipart/form-data):**
- `name` (string, optional): Updated document name
- `description` (string, optional): Updated document description
- `is_active` (boolean, optional): Updated active status

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Updated Document Name",
    "description": "Updated description",
    "is_active": true,
    "updated_at": "2025-01-01T00:00:00Z"
  }
}
```

### 5. Delete Document
**DELETE** `/api/v1/documents/{document_id}`

Soft delete a document (marks as deleted but preserves data).

**Response:**
```json
{
  "success": true,
  "data": {
    "document_id": "uuid",
    "deleted": true
  }
}
```

### 6. Update Document Status
**PATCH** `/api/v1/documents/{document_id}/status`

Update document active/inactive status.

**Request:**
```json
{
  "is_active": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "Document Name",
    "is_active": false,
    "updated_at": "2025-01-01T00:00:00Z"
  }
}
```

### 7. Get Document Status
**GET** `/api/v1/documents/{document_id}/status`

Get document status information.

**Response:**
```json
{
  "success": true,
  "data": {
    "document_id": "uuid",
    "name": "Document Name",
    "is_active": true,
    "is_deleted": false,
    "created_at": "2025-01-01T00:00:00Z",
    "updated_at": "2025-01-01T00:00:00Z",
    "deleted_at": null
  }
}
```

### 8. Download Document
**GET** `/api/v1/documents/{document_id}/download`

Get a presigned download URL for the document.

**Response:**
```json
{
  "success": true,
  "data": {
    "download_url": "https://presigned-s3-url.com/file.pdf?expires=..."
  }
}
```

## Franchisor Document Endpoints

### Get Franchisor Documents
**GET** `/api/v1/franchisors/{franchisor_id}/documents`

Get all documents associated with a specific franchisor.

**Query Parameters:**
- `is_active` (boolean, optional): Filter by active status

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "name": "Document Name",
      "description": "Document description",
      "file_type": "application/pdf",
      "file_size": "1024",
      "file_path": "documents/file.pdf",
      "file_url": "https://s3.amazonaws.com/bucket/file.pdf",
      "user_id": "uuid",
      "franchisor_id": "uuid",
      "is_active": true,
      "is_deleted": false,
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z",
      "deleted_at": null
    }
  ]
}
```

## Error Responses

All endpoints return standardized error responses:

```json
{
  "success": false,
  "message": {
    "title": "Error Title",
    "description": "Detailed error description"
  },
  "data": null,
  "error_code": "ERROR_CODE"
}
```

## Common Error Codes
- `VALIDATION_ERROR`: Invalid request data
- `NOT_FOUND`: Resource not found
- `UPLOAD_ERROR`: File upload failed
- `DATABASE_ERROR`: Database operation failed
- `AUTHENTICATION_ERROR`: Invalid or missing authentication

## Features

### File Storage
- **S3 Integration**: All files are stored in AWS S3
- **Presigned URLs**: Secure download links with expiration
- **File Validation**: Automatic file type and size validation

### Search and Filtering
- **Full-text Search**: Search across document names and descriptions
- **Multiple Filters**: Filter by franchisor, file type, status
- **Sorting**: Sort by name, creation date, etc.
- **Pagination**: Efficient pagination for large datasets

### Security
- **Authentication Required**: All endpoints require valid JWT tokens
- **User Isolation**: Users can only access their own documents
- **Soft Delete**: Documents are marked as deleted, not permanently removed

### Data Management
- **Audit Trail**: Created/updated timestamps for all documents
- **Status Management**: Active/inactive status for documents
- **Metadata Storage**: File type, size, and path information

## Notes

- **Ingestion Disabled**: Document ingestion and processing features have been removed from this system
- **Clean CRUD**: Focus on basic document management operations
- **S3 Dependency**: Requires properly configured AWS S3 service
- **Database**: Uses PostgreSQL with proper indexing for performance
