#!/usr/bin/env python3
"""
Test Document Upload with Real PDF File
Tests the document management endpoints using the Coochie Information pack PDF
"""

import asyncio
import aiohttp
import aiofiles
import json
import os
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_PDF_PATH = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"

# Test credentials (you'll need to replace with actual credentials)
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "testpassword"

class DocumentUploadTester:
    def __init__(self):
        self.session = None
        self.auth_token = None
        self.uploaded_document_id = None

    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def authenticate(self):
        """Authenticate and get JWT token"""
        try:
            auth_data = {
                "email": TEST_EMAIL,
                "password": TEST_PASSWORD
            }
            
            async with self.session.post(f"{API_BASE_URL}/api/auth/login", json=auth_data) as response:
                if response.status == 200:
                    data = await response.json()
                    if data.get("success") and "access_token" in data.get("data", {}):
                        self.auth_token = data["data"]["access_token"]
                        print("✅ Authentication successful")
                        return True
                    else:
                        print(f"❌ Authentication failed: {data}")
                        return False
                else:
                    print(f"❌ Authentication failed with status {response.status}")
                    return False
        except Exception as e:
            print(f"❌ Authentication error: {e}")
            return False

    def get_headers(self):
        """Get headers with authentication"""
        if not self.auth_token:
            return {}
        return {"Authorization": f"Bearer {self.auth_token}"}

    async def test_file_exists(self):
        """Check if test PDF file exists"""
        if not os.path.exists(TEST_PDF_PATH):
            print(f"❌ Test file not found: {TEST_PDF_PATH}")
            return False
        
        file_size = os.path.getsize(TEST_PDF_PATH)
        print(f"✅ Test file found: {TEST_PDF_PATH} ({file_size} bytes)")
        return True

    async def test_upload_document_v1(self):
        """Test document upload using V1 API"""
        print("\n📤 Testing V1 API Upload: POST /api/v1/documents/upload")
        
        try:
            data = aiohttp.FormData()
            data.add_field('name', 'Coochie Information Pack')
            data.add_field('description', 'Test upload of Coochie Hydrogreen information pack')
            data.add_field('is_active', 'true')
            
            async with aiofiles.open(TEST_PDF_PATH, 'rb') as f:
                file_content = await f.read()
                data.add_field('file', file_content, filename='Coochie_Information_pack.pdf', content_type='application/pdf')
            
            async with self.session.post(
                f"{API_BASE_URL}/api/v1/documents/upload",
                data=data,
                headers=self.get_headers()
            ) as response:
                response_data = await response.json()
                
                if response.status == 201 and response_data.get("success"):
                    self.uploaded_document_id = response_data["data"]["id"]
                    print(f"✅ V1 Upload successful! Document ID: {self.uploaded_document_id}")
                    print(f"   File URL: {response_data['data'].get('file_url', 'N/A')}")
                    return True
                else:
                    print(f"❌ V1 Upload failed: {response.status} - {response_data}")
                    return False
                    
        except Exception as e:
            print(f"❌ V1 Upload error: {e}")
            return False

    async def test_upload_document_compat(self):
        """Test document upload using compatibility API"""
        print("\n📤 Testing Compatibility API Upload: POST /api/documents/upload")
        
        try:
            data = aiohttp.FormData()
            data.add_field('name', 'Coochie Information Pack (Compat)')
            data.add_field('description', 'Test upload using compatibility API')
            data.add_field('is_active', 'true')
            
            async with aiofiles.open(TEST_PDF_PATH, 'rb') as f:
                file_content = await f.read()
                data.add_field('file', file_content, filename='Coochie_Information_pack.pdf', content_type='application/pdf')
            
            async with self.session.post(
                f"{API_BASE_URL}/api/documents/upload",
                data=data,
                headers=self.get_headers()
            ) as response:
                response_data = await response.json()
                
                if response.status == 201 and response_data.get("success"):
                    print(f"✅ Compatibility Upload successful! Document ID: {response_data['data']['id']}")
                    print(f"   File URL: {response_data['data'].get('file_url', 'N/A')}")
                    return True
                else:
                    print(f"❌ Compatibility Upload failed: {response.status} - {response_data}")
                    return False
                    
        except Exception as e:
            print(f"❌ Compatibility Upload error: {e}")
            return False

    async def test_get_document_status(self):
        """Test getting document status"""
        if not self.uploaded_document_id:
            print("⏭️  Skipping status test - no uploaded document")
            return False
            
        print(f"\n📊 Testing Document Status: GET /api/documents/{self.uploaded_document_id}/status")
        
        try:
            async with self.session.get(
                f"{API_BASE_URL}/api/documents/{self.uploaded_document_id}/status",
                headers=self.get_headers()
            ) as response:
                response_data = await response.json()
                
                if response.status == 200 and response_data.get("success"):
                    print("✅ Status retrieval successful!")
                    print(f"   Document ID: {response_data['data']['document_id']}")
                    print(f"   Name: {response_data['data']['name']}")
                    print(f"   Active: {response_data['data']['is_active']}")
                    print(f"   Deleted: {response_data['data']['is_deleted']}")
                    return True
                else:
                    print(f"❌ Status retrieval failed: {response.status} - {response_data}")
                    return False
                    
        except Exception as e:
            print(f"❌ Status retrieval error: {e}")
            return False

    async def test_get_download_url(self):
        """Test getting download URL"""
        if not self.uploaded_document_id:
            print("⏭️  Skipping download test - no uploaded document")
            return False
            
        print(f"\n⬇️  Testing Download URL: GET /api/documents/{self.uploaded_document_id}/download")
        
        try:
            async with self.session.get(
                f"{API_BASE_URL}/api/documents/{self.uploaded_document_id}/download",
                headers=self.get_headers()
            ) as response:
                response_data = await response.json()
                
                if response.status == 200 and response_data.get("success"):
                    print("✅ Download URL generation successful!")
                    print(f"   Download URL: {response_data['data']['download_url'][:100]}...")
                    return True
                else:
                    print(f"❌ Download URL generation failed: {response.status} - {response_data}")
                    return False
                    
        except Exception as e:
            print(f"❌ Download URL generation error: {e}")
            return False

    async def test_list_documents(self):
        """Test listing documents"""
        print("\n📋 Testing Document List: GET /api/documents/")
        
        try:
            async with self.session.get(
                f"{API_BASE_URL}/api/documents/?limit=5",
                headers=self.get_headers()
            ) as response:
                response_data = await response.json()
                
                if response.status == 200 and response_data.get("success"):
                    items = response_data["data"]["items"]
                    total = response_data["data"]["total_count"]
                    print(f"✅ Document list retrieval successful!")
                    print(f"   Total documents: {total}")
                    print(f"   Retrieved: {len(items)} documents")
                    
                    for doc in items[:3]:  # Show first 3
                        print(f"   - {doc['name']} ({doc['file_type']})")
                    
                    return True
                else:
                    print(f"❌ Document list retrieval failed: {response.status} - {response_data}")
                    return False
                    
        except Exception as e:
            print(f"❌ Document list retrieval error: {e}")
            return False

    async def run_all_tests(self):
        """Run all document management tests"""
        print("🧪 Starting Document Management API Tests")
        print("=" * 50)
        
        # Check if file exists
        if not await self.test_file_exists():
            return False
        
        # Authenticate (skip if no credentials)
        print("\n🔐 Authentication Test")
        if not await self.authenticate():
            print("⚠️  Authentication failed - testing without auth (will expect 401 errors)")
        
        # Test results
        results = []
        
        # Test uploads
        results.append(await self.test_upload_document_v1())
        results.append(await self.test_upload_document_compat())
        
        # Test other operations
        results.append(await self.test_get_document_status())
        results.append(await self.test_get_download_url())
        results.append(await self.test_list_documents())
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Test Results Summary")
        print("=" * 50)
        
        passed = sum(results)
        total = len(results)
        
        print(f"✅ Passed: {passed}/{total}")
        print(f"❌ Failed: {total - passed}/{total}")
        
        if passed == total:
            print("\n🎉 All tests passed! Document management system is working correctly.")
        else:
            print(f"\n⚠️  Some tests failed. Check the output above for details.")
        
        return passed == total


async def main():
    """Main test function"""
    async with DocumentUploadTester() as tester:
        await tester.run_all_tests()


if __name__ == "__main__":
    print("🚀 Document Management API Tester")
    print("Using test file: Coochie_Information pack.pdf")
    print()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
