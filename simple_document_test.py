#!/usr/bin/env python3
"""
Simple Document Management Test
Tests document endpoints using requests library with the Coochie PDF
"""

import requests
import os
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_PDF_PATH = "/Users/<USER>/Projects/Python Projects/growthhive-cursor/Coochie_Information pack.pdf"

def test_file_exists():
    """Check if test PDF file exists"""
    if not os.path.exists(TEST_PDF_PATH):
        print(f"❌ Test file not found: {TEST_PDF_PATH}")
        return False
    
    file_size = os.path.getsize(TEST_PDF_PATH)
    print(f"✅ Test file found: {TEST_PDF_PATH} ({file_size:,} bytes)")
    return True

def test_endpoints_without_auth():
    """Test that endpoints properly require authentication"""
    print("\n🔒 Testing Authentication Requirements")
    print("-" * 40)
    
    endpoints = [
        ("GET", "/api/documents/"),
        ("GET", "/api/v1/documents/"),
        ("GET", "/api/documents/test-id"),
        ("GET", "/api/documents/test-id/status"),
        ("GET", "/api/documents/test-id/download"),
    ]
    
    auth_required = 0
    for method, endpoint in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{API_BASE_URL}{endpoint}", timeout=5)
            
            if response.status_code == 401:
                print(f"✅ {method} {endpoint} - Requires auth (401)")
                auth_required += 1
            else:
                print(f"⚠️  {method} {endpoint} - Status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {method} {endpoint} - Connection error: {e}")
    
    print(f"\n📊 Authentication check: {auth_required}/{len(endpoints)} endpoints require auth")
    return auth_required > 0

def test_api_health():
    """Test basic API health"""
    print("\n🏥 Testing API Health")
    print("-" * 40)
    
    try:
        # Test root endpoint
        response = requests.get(f"{API_BASE_URL}/", timeout=5)
        if response.status_code == 200:
            print("✅ Root endpoint accessible")
            data = response.json()
            if "docs" in data.get("data", {}):
                print(f"✅ API docs available at: {data['data']['docs']}")
        else:
            print(f"⚠️  Root endpoint returned: {response.status_code}")
            
        # Test OpenAPI docs
        response = requests.get(f"{API_BASE_URL}/api/docs", timeout=5)
        if response.status_code == 200:
            print("✅ OpenAPI documentation accessible")
        else:
            print(f"⚠️  OpenAPI docs returned: {response.status_code}")
            
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API health check failed: {e}")
        return False

def test_document_endpoints_structure():
    """Test that document endpoints are properly configured"""
    print("\n🏗️  Testing Document Endpoint Structure")
    print("-" * 40)
    
    # Test that endpoints exist (should return 401 for auth, not 404)
    endpoints_to_test = [
        # V1 API endpoints
        ("GET", "/api/v1/documents/", "List documents (V1)"),
        ("GET", "/api/v1/documents/test-id", "Get document by ID (V1)"),
        ("GET", "/api/v1/documents/test-id/status", "Get document status (V1)"),
        ("GET", "/api/v1/documents/test-id/download", "Get download URL (V1)"),
        
        # Compatibility endpoints
        ("GET", "/api/documents/", "List documents (Compat)"),
        ("GET", "/api/documents/test-id", "Get document by ID (Compat)"),
        ("GET", "/api/documents/test-id/status", "Get document status (Compat)"),
        ("GET", "/api/documents/test-id/download", "Get download URL (Compat)"),
    ]
    
    configured_endpoints = 0
    for method, endpoint, description in endpoints_to_test:
        try:
            response = requests.get(f"{API_BASE_URL}{endpoint}", timeout=5)
            
            if response.status_code == 401:
                print(f"✅ {description} - Properly configured")
                configured_endpoints += 1
            elif response.status_code == 404:
                print(f"❌ {description} - Not found (404)")
            else:
                print(f"⚠️  {description} - Status: {response.status_code}")
                configured_endpoints += 1  # Still configured, just different response
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {description} - Connection error: {e}")
    
    print(f"\n📊 Endpoint configuration: {configured_endpoints}/{len(endpoints_to_test)} endpoints configured")
    return configured_endpoints == len(endpoints_to_test)

def test_file_upload_structure():
    """Test upload endpoint structure (without actually uploading)"""
    print("\n📤 Testing Upload Endpoint Structure")
    print("-" * 40)
    
    upload_endpoints = [
        "/api/v1/documents/upload",
        "/api/documents/upload"
    ]
    
    for endpoint in upload_endpoints:
        try:
            # Try POST without data (should return 401 for auth or 422 for validation)
            response = requests.post(f"{API_BASE_URL}{endpoint}", timeout=5)
            
            if response.status_code in [401, 422]:
                print(f"✅ {endpoint} - Properly configured (Status: {response.status_code})")
            elif response.status_code == 404:
                print(f"❌ {endpoint} - Not found")
            else:
                print(f"⚠️  {endpoint} - Status: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint} - Connection error: {e}")

def main():
    """Main test function"""
    print("🧪 Simple Document Management API Test")
    print("=" * 50)
    print(f"API Base URL: {API_BASE_URL}")
    print(f"Test File: {TEST_PDF_PATH}")
    print()
    
    # Run tests
    tests_passed = 0
    total_tests = 4
    
    # Test 1: File exists
    if test_file_exists():
        tests_passed += 1
    
    # Test 2: API health
    if test_api_health():
        tests_passed += 1
    
    # Test 3: Authentication requirements
    if test_endpoints_without_auth():
        tests_passed += 1
    
    # Test 4: Endpoint structure
    if test_document_endpoints_structure():
        tests_passed += 1
    
    # Test 5: Upload endpoint structure
    test_file_upload_structure()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    print(f"✅ Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("\n🎉 All basic tests passed!")
        print("✅ Document management endpoints are properly configured")
        print("✅ Authentication is working")
        print("✅ Both V1 and compatibility APIs are available")
        print()
        print("📝 Next steps:")
        print("1. Start the FastAPI server: uvicorn app.main:app --reload")
        print("2. Get authentication token from /api/auth/login")
        print("3. Test actual file upload with authentication")
    else:
        print(f"\n⚠️  {total_tests - tests_passed} tests failed")
        print("Please check the FastAPI server is running and endpoints are configured correctly")
    
    print(f"\n📚 API Documentation: {API_BASE_URL}/api/docs")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n⏹️  Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
